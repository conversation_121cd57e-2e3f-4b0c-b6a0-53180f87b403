#!/bin/bash

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${GREEN}"
echo "========================================"
echo "    🚀 ATMA Backend Quick Start"
echo "========================================"
echo -e "${NC}"

echo -e "${BLUE}📥 Pulling latest images...${NC}"
docker-compose pull

echo ""
echo -e "${BLUE}🏗️ Starting all services...${NC}"
docker-compose up --build -d \
    postgres redis rabbitmq \
    auth-service assessment-service archive-service notification-service \
    api-gateway testing-service \
    analysis-worker-1 analysis-worker-2 analysis-worker-3 analysis-worker-4 analysis-worker-5 \
    analysis-worker-6 analysis-worker-7 analysis-worker-8 analysis-worker-9 analysis-worker-10 \
    documentation-service cloudflared

echo ""
echo -e "${YELLOW}⏳ Waiting for services to start...${NC}"
sleep 15

echo ""
echo -e "${BLUE}📊 Service Status:${NC}"
docker-compose ps

echo ""
echo -e "${GREEN}"
echo "========================================"
echo "    ✅ ATMA Backend Started!"
echo "========================================"
echo -e "${NC}"
echo ""
echo -e "${CYAN}🌐 Available Services:${NC}"
echo "   API Gateway: http://localhost:3000"
echo "   Auth Service: http://localhost:3001"
echo "   Archive Service: http://localhost:3002"
echo "   Assessment Service: http://localhost:3003"
echo "   Notification Service: http://localhost:3005"
echo "   Documentation: http://localhost:8080"
echo "   RabbitMQ: http://localhost:15672 (admin/admin123)"
echo ""
echo -e "${YELLOW}🧪 To run tests:${NC}"
echo "   node testing/e2e-test.js"
echo "   node testing/load-test.js"
echo ""
echo -e "${YELLOW}🔧 To check status:${NC}"
echo "   docker-compose ps"
echo ""
