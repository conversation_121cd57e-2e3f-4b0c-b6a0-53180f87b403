# ATMA Testing Script (PowerShell)
# This script runs E2E and Load tests

param(
    [string]$TestType = "both"  # "e2e", "load", or "both"
)

# Function to print colored output
function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# Check if services are running
function Test-Services {
    Write-Status "Checking if services are running..."
    
    $requiredServices = @("postgres", "redis", "rabbitmq", "auth-service", "api-gateway")
    $missingServices = @()
    
    foreach ($service in $requiredServices) {
        $status = docker-compose ps $service
        if ($status -notmatch "Up") {
            $missingServices += $service
        }
    }
    
    if ($missingServices.Count -gt 0) {
        Write-Error "The following required services are not running: $($missingServices -join ', ')"
        Write-Status "Please run '.\startup.ps1' first to start all services"
        return $false
    }
    
    Write-Success "All required services are running!"
    return $true
}

# Check if analysis workers are running
function Test-Workers {
    Write-Status "Checking analysis workers..."
    
    $runningWorkers = 0
    for ($i = 1; $i -le 10; $i++) {
        $status = docker-compose ps "analysis-worker-$i"
        if ($status -match "Up") {
            $runningWorkers++
        }
    }
    
    if ($runningWorkers -eq 0) {
        Write-Error "No analysis workers are running!"
        Write-Status "Starting analysis workers..."
        docker-compose up -d analysis-worker-1 analysis-worker-2 analysis-worker-3 analysis-worker-4 analysis-worker-5 analysis-worker-6 analysis-worker-7 analysis-worker-8 analysis-worker-9 analysis-worker-10
        
        Write-Status "Waiting for workers to be ready..."
        Start-Sleep -Seconds 10
    } else {
        Write-Success "$runningWorkers analysis workers are running!"
    }
    
    return $true
}

# Run E2E test
function Invoke-E2ETest {
    Write-Status "=== Running E2E Test ==="
    try {
        node testing/e2e-test.js
        Write-Success "E2E test completed successfully!"
        return $true
    } catch {
        Write-Error "E2E test failed: $_"
        return $false
    }
}

# Run Load test
function Invoke-LoadTest {
    Write-Status "=== Running Load Test ==="
    try {
        node testing/load-test.js
        Write-Success "Load test completed successfully!"
        return $true
    } catch {
        Write-Error "Load test failed: $_"
        return $false
    }
}

# Main execution
try {
    Write-Status "🧪 ATMA Testing Suite"
    Write-Status "====================="
    
    # Check prerequisites
    if (!(Test-Services)) {
        exit 1
    }
    
    if (!(Test-Workers)) {
        exit 1
    }
    
    # Wait a bit for services to be fully ready
    Write-Status "Waiting for services to be fully ready..."
    Start-Sleep -Seconds 5
    
    $e2eResult = $true
    $loadResult = $true
    
    # Run tests based on arguments
    if ($TestType -eq "e2e") {
        $e2eResult = Invoke-E2ETest
    } elseif ($TestType -eq "load") {
        $loadResult = Invoke-LoadTest
    } else {
        # Run both tests
        $e2eResult = Invoke-E2ETest
        
        if ($e2eResult) {
            Write-Status "Waiting 5 seconds before load test..."
            Start-Sleep -Seconds 5
            $loadResult = Invoke-LoadTest
        }
    }
    
    # Summary
    Write-Status "=== Test Summary ==="
    if ($TestType -ne "load") {
        if ($e2eResult) {
            Write-Success "✅ E2E Test: PASSED"
        } else {
            Write-Error "❌ E2E Test: FAILED"
        }
    }
    
    if ($TestType -ne "e2e" -and $e2eResult) {
        if ($loadResult) {
            Write-Success "✅ Load Test: PASSED"
        } else {
            Write-Error "❌ Load Test: FAILED"
        }
    }
    
    if ($e2eResult -and $loadResult) {
        Write-Success "🎉 All tests passed!"
        exit 0
    } else {
        Write-Error "❌ Some tests failed!"
        exit 1
    }

} catch {
    Write-Error "Testing failed: $_"
    exit 1
}

# Show usage if help requested
if ($TestType -eq "help" -or $TestType -eq "--help" -or $TestType -eq "-h") {
    Write-Host "Usage: .\run-tests.ps1 [-TestType <type>]"
    Write-Host ""
    Write-Host "Parameters:"
    Write-Host "  -TestType   Specify test type: 'e2e', 'load', or 'both' (default: 'both')"
    Write-Host ""
    Write-Host "Examples:"
    Write-Host "  .\run-tests.ps1                    # Run both tests"
    Write-Host "  .\run-tests.ps1 -TestType e2e      # Run only E2E test"
    Write-Host "  .\run-tests.ps1 -TestType load     # Run only Load test"
    exit 0
}
