#!/bin/bash

# ATMA Testing Script
# This script runs E2E and Load tests

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if services are running
check_services() {
    print_status "Checking if services are running..."
    
    local required_services=("postgres" "redis" "rabbitmq" "auth-service" "api-gateway")
    local missing_services=()
    
    for service in "${required_services[@]}"; do
        if ! docker-compose ps $service | grep -q "Up"; then
            missing_services+=($service)
        fi
    done
    
    if [ ${#missing_services[@]} -ne 0 ]; then
        print_error "The following required services are not running: ${missing_services[*]}"
        print_status "Please run './startup.sh' first to start all services"
        exit 1
    fi
    
    print_success "All required services are running!"
}

# Check if analysis workers are running
check_workers() {
    print_status "Checking analysis workers..."
    
    local running_workers=0
    for i in {1..10}; do
        if docker-compose ps analysis-worker-$i | grep -q "Up"; then
            running_workers=$((running_workers + 1))
        fi
    done
    
    if [ $running_workers -eq 0 ]; then
        print_error "No analysis workers are running!"
        print_status "Starting analysis workers..."
        docker-compose up -d \
            analysis-worker-1 analysis-worker-2 analysis-worker-3 analysis-worker-4 analysis-worker-5 \
            analysis-worker-6 analysis-worker-7 analysis-worker-8 analysis-worker-9 analysis-worker-10
        
        print_status "Waiting for workers to be ready..."
        sleep 10
    else
        print_success "$running_workers analysis workers are running!"
    fi
}

# Run E2E test
run_e2e_test() {
    print_status "=== Running E2E Test ==="
    if node testing/e2e-test.js; then
        print_success "E2E test completed successfully!"
        return 0
    else
        print_error "E2E test failed!"
        return 1
    fi
}

# Run Load test
run_load_test() {
    print_status "=== Running Load Test ==="
    if node testing/load-test.js; then
        print_success "Load test completed successfully!"
        return 0
    else
        print_error "Load test failed!"
        return 1
    fi
}

# Main execution
main() {
    print_status "🧪 ATMA Testing Suite"
    print_status "====================="
    
    # Check prerequisites
    check_services
    check_workers
    
    # Wait a bit for services to be fully ready
    print_status "Waiting for services to be fully ready..."
    sleep 5
    
    local e2e_result=0
    local load_result=0
    
    # Run tests based on arguments
    if [ "$1" = "e2e" ]; then
        run_e2e_test
        e2e_result=$?
    elif [ "$1" = "load" ]; then
        run_load_test
        load_result=$?
    else
        # Run both tests
        run_e2e_test
        e2e_result=$?
        
        if [ $e2e_result -eq 0 ]; then
            print_status "Waiting 5 seconds before load test..."
            sleep 5
            run_load_test
            load_result=$?
        fi
    fi
    
    # Summary
    print_status "=== Test Summary ==="
    if [ "$1" != "load" ]; then
        if [ $e2e_result -eq 0 ]; then
            print_success "✅ E2E Test: PASSED"
        else
            print_error "❌ E2E Test: FAILED"
        fi
    fi
    
    if [ "$1" != "e2e" ] && [ $e2e_result -eq 0 ]; then
        if [ $load_result -eq 0 ]; then
            print_success "✅ Load Test: PASSED"
        else
            print_error "❌ Load Test: FAILED"
        fi
    fi
    
    if [ $e2e_result -eq 0 ] && [ $load_result -eq 0 ]; then
        print_success "🎉 All tests passed!"
        exit 0
    else
        print_error "❌ Some tests failed!"
        exit 1
    fi
}

# Show usage if help requested
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "Usage: $0 [e2e|load]"
    echo ""
    echo "Options:"
    echo "  e2e     Run only E2E test"
    echo "  load    Run only Load test"
    echo "  (none)  Run both tests"
    echo ""
    echo "Examples:"
    echo "  $0           # Run both tests"
    echo "  $0 e2e       # Run only E2E test"
    echo "  $0 load      # Run only Load test"
    exit 0
fi

# Run main function
main "$1"
