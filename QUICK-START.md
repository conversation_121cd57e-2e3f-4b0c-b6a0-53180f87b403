# 🚀 ATMA Backend Quick Start Guide

Panduan lengkap untuk menjalankan semua services ATMA Backend dengan mudah!

## 📋 Prerequisites

- Docker & Docker Compose terinstall
- Node.js (untuk testing)
- PowerShell (Windows) atau Bash (Linux/Mac)

## 🎯 Cara Termudah - <PERSON>tu <PERSON>ah!

### **Windows (PowerShell) - RECOMMENDED**

```powershell
# Quick start - semua services termasuk 10 workers + Cloudflare
.\quick-start.ps1

# Atau dengan opsi:
.\quick-start.ps1 -Clean              # Clean start (hapus containers lama)
.\quick-start.ps1 -NoPull             # Skip pulling images
.\quick-start.ps1 -NoCloudflare       # Skip Cloudflare tunnel
```

### **Windows (Batch File)**

```cmd
# Double-click atau jalankan:
start-all.bat
```

### **Linux/Mac (Bash)**

```bash
# Make executable first:
chmod +x start-all.sh

# Run:
./start-all.sh
```

## 🔧 Advanced Options

### **Startup dengan Staging (Lebih Stabil)**

```powershell
# Startup bertahap dengan health checks
.\startup.ps1

# Opsi:
.\startup.ps1 -SkipCleanup           # Skip cleanup
.\startup.ps1 -SkipPull              # Skip pulling images
```

### **Testing**

```powershell
# Jalankan semua tests
.\run-tests.ps1

# Jalankan test tertentu
.\run-tests.ps1 -TestType e2e        # E2E test only
.\run-tests.ps1 -TestType load       # Load test only
```

### **Cloudflare Troubleshooting**

```powershell
.\fix-cloudflare.ps1 check           # Check prerequisites
.\fix-cloudflare.ps1 start           # Start tunnel
.\fix-cloudflare.ps1 status          # Check status
.\fix-cloudflare.ps1 logs            # View logs
.\fix-cloudflare.ps1 restart         # Restart tunnel
```

## 📊 Services yang Akan Berjalan

| Service | Port | Status Check |
|---------|------|--------------|
| **Infrastructure** | | |
| PostgreSQL | 5432 | ✅ Auto-started |
| Redis | 6379 | ✅ Auto-started |
| RabbitMQ | 5672, 15672 | ✅ Auto-started |
| **Application** | | |
| Auth Service | 3001 | ✅ Auto-started |
| Assessment Service | 3003 | ✅ Auto-started |
| Archive Service | 3002 | ✅ Auto-started |
| Notification Service | 3005 | ✅ Auto-started |
| API Gateway | 3000 | ✅ Auto-started |
| **Workers** | | |
| Analysis Worker 1-10 | - | ✅ Auto-started |
| **Additional** | | |
| Testing Service | - | ✅ Auto-started |
| Documentation | 8080 | ✅ Auto-started |
| Cloudflare Tunnel | - | ✅ Auto-started |

## 🌐 Available Endpoints

- **API Gateway**: http://localhost:3000
- **Auth Service**: http://localhost:3001
- **Archive Service**: http://localhost:3002
- **Assessment Service**: http://localhost:3003
- **Notification Service**: http://localhost:3005
- **Documentation**: http://localhost:8080
- **RabbitMQ Management**: http://localhost:15672 (admin/admin123)

## 🧪 Testing

Setelah semua services berjalan:

```bash
# E2E Test
node testing/e2e-test.js

# Load Test
node testing/load-test.js
```

## 🔍 Monitoring & Troubleshooting

### **Check Status**
```bash
docker-compose ps
```

### **View Logs**
```bash
# All services
docker-compose logs

# Specific service
docker-compose logs auth-service
docker-compose logs analysis-worker-1

# Follow logs
docker-compose logs -f api-gateway
```

### **Restart Services**
```bash
# Restart specific service
docker-compose restart auth-service

# Restart all
docker-compose restart
```

### **Stop All**
```bash
docker-compose down
```

### **Clean Everything**
```bash
docker-compose down --volumes --remove-orphans
docker system prune -a --volumes -f
```

## ⚡ Performance Tips

1. **Untuk Development**: Gunakan `.\quick-start.ps1 -NoPull` untuk skip pulling images
2. **Untuk Production**: Gunakan `.\startup.ps1` untuk startup bertahap dengan health checks
3. **Memory**: Pastikan Docker memiliki minimal 4GB RAM
4. **CPU**: Minimal 4 cores untuk optimal performance

## 🐛 Common Issues

### **Port Already in Use**
```bash
# Check what's using the port
netstat -ano | findstr :3000

# Kill process
taskkill /PID <PID> /F
```

### **Docker Out of Space**
```bash
docker system prune -a --volumes -f
```

### **Services Not Healthy**
```bash
# Check logs
docker-compose logs <service-name>

# Restart service
docker-compose restart <service-name>
```

### **Cloudflare Not Working**
```powershell
.\fix-cloudflare.ps1 check
.\fix-cloudflare.ps1 start
```

## 📝 Quick Commands Cheat Sheet

```powershell
# Start everything
.\quick-start.ps1

# Run tests
.\run-tests.ps1

# Check status
docker-compose ps

# View logs
docker-compose logs -f

# Stop everything
docker-compose down

# Clean restart
.\quick-start.ps1 -Clean
```

---

**🎉 Happy Coding!** 

Jika ada masalah, check logs atau gunakan script troubleshooting yang tersedia.
