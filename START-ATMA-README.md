# ATMA Universal Startup Scripts

Script universal untuk memulai semua layanan ATMA Backend yang menggantikan semua script startup yang redundan.

## 📁 File Script

- **`start-atma.sh`** - Script untuk Linux/macOS/WSL
- **`start-atma.ps1`** - Script untuk Windows PowerShell
- **`start-atma.bat`** - Script wrapper untuk Windows Command Prompt

## 🚀 Cara Penggunaan

### Linux/macOS/WSL

```bash
# Berikan permission execute
chmod +x start-atma.sh

# Penggunaan normal
./start-atma.sh

# Quick start (tanpa cleanup dan pull)
./start-atma.sh --quick

# Clean start (dengan cleanup penuh)
./start-atma.sh --clean

# Start dengan 5 analysis workers
./start-atma.sh --workers=5

# Simple mode (lebih cepat, tanpa health check)
./start-atma.sh --simple

# Lihat status layanan
./start-atma.sh --status

# Stop semua layanan
./start-atma.sh --stop

# Restart semua layanan
./start-atma.sh --restart
```

### Windows PowerShell

```powershell
# Penggunaan normal
.\start-atma.ps1

# Quick start
.\start-atma.ps1 -Quick

# Clean start
.\start-atma.ps1 -Clean

# Start dengan 5 analysis workers
.\start-atma.ps1 -Workers 5

# Simple mode
.\start-atma.ps1 -Simple

# Lihat status layanan
.\start-atma.ps1 -Status

# Stop semua layanan
.\start-atma.ps1 -Stop

# Restart semua layanan
.\start-atma.ps1 -Restart
```

### Windows Command Prompt

```cmd
REM Penggunaan normal
start-atma.bat

REM Quick start
start-atma.bat --quick

REM Clean start
start-atma.bat --clean

REM Start dengan 5 analysis workers
start-atma.bat --workers=5

REM Simple mode
start-atma.bat --simple

REM Lihat status layanan
start-atma.bat --status

REM Stop semua layanan
start-atma.bat --stop
```

## ⚙️ Opsi Lengkap

### Opsi Umum

| Opsi | Deskripsi |
|------|-----------|
| `-h, --help` | Tampilkan bantuan |
| `-v, --version` | Tampilkan versi script |
| `-q, --quick` | Quick start (skip cleanup dan pull) |
| `-c, --clean` | Clean start (hapus container dan image) |
| `--no-pull` | Skip pulling image terbaru |
| `--no-cloudflare` | Skip Cloudflare tunnel |
| `--no-docs` | Skip documentation service |
| `--workers=N` | Jumlah analysis workers (default: 10) |
| `--wait=N` | Waktu tunggu untuk layanan (default: 15 detik) |
| `--simple` | Simple mode (start semua sekaligus) |
| `--staged` | Staged mode (start bertahap dengan health check) |
| `--status` | Tampilkan status layanan saat ini |
| `--stop` | Stop semua layanan |
| `--restart` | Restart semua layanan |

### Mode Operasi

#### 1. **Staged Mode** (Default)
- Start layanan secara bertahap dengan urutan yang benar
- Menunggu setiap layanan menjadi healthy sebelum melanjutkan
- Lebih lambat tapi lebih reliable

#### 2. **Simple Mode**
- Start semua layanan sekaligus
- Lebih cepat tapi kurang reliable
- Cocok untuk development

## 🔧 Persyaratan

- Docker dan Docker Compose terinstall
- PowerShell (untuk Windows)
- Bash (untuk Linux/macOS)

## 📊 Layanan yang Dijalankan

### Infrastructure Services
- PostgreSQL (Database)
- Redis (Cache)
- RabbitMQ (Message Queue)

### Core Application Services
- Auth Service (Port 3001)
- Assessment Service (Port 3003)
- Archive Service (Port 3002)
- Notification Service (Port 3005)

### Gateway & Workers
- API Gateway (Port 3000) - Main Entry Point
- Analysis Workers (1-10 instances)
- Testing Service

### Optional Services
- Documentation Service (Port 8080)
- Cloudflare Tunnel

## 🌐 Endpoint yang Tersedia

Setelah semua layanan berjalan:

- **API Gateway**: http://localhost:3000
- **Auth Service**: http://localhost:3001
- **Archive Service**: http://localhost:3002
- **Assessment Service**: http://localhost:3003
- **Notification Service**: http://localhost:3005
- **Documentation**: http://localhost:8080
- **RabbitMQ Management**: http://localhost:15672 (admin/admin123)

## 🧪 Menjalankan Test

```bash
# Linux/macOS
./run-tests.sh
node testing/e2e-test.js
node testing/load-test.js

# Windows
.\run-tests.ps1
node testing/e2e-test.js
node testing/load-test.js
```

## 🔍 Troubleshooting

### 1. Docker tidak berjalan
```
ERROR: Docker is not running. Please start Docker first.
```
**Solusi**: Start Docker Desktop atau Docker daemon

### 2. Port sudah digunakan
```
ERROR: Port 3000 is already in use
```
**Solusi**: Stop layanan yang menggunakan port tersebut atau gunakan `--stop` untuk menghentikan layanan ATMA

### 3. Layanan tidak healthy
```
WARNING: auth-service failed to become healthy
```
**Solusi**: Periksa log dengan `docker-compose logs auth-service`

### 4. Cloudflare tunnel gagal
```
WARNING: Cloudflare tunnel failed to start (this is optional)
```
**Solusi**: Periksa konfigurasi `CLOUDFLARE_TUNNEL_TOKEN` di file `.env`

## 📝 Contoh Penggunaan

### Development Harian
```bash
# Quick start untuk development
./start-atma.sh --quick --simple
```

### Production Setup
```bash
# Clean start dengan semua layanan
./start-atma.sh --clean --staged
```

### Testing Environment
```bash
# Start tanpa Cloudflare dan docs
./start-atma.sh --no-cloudflare --no-docs --workers=3
```

### Maintenance
```bash
# Check status
./start-atma.sh --status

# Restart jika ada masalah
./start-atma.sh --restart

# Stop semua untuk maintenance
./start-atma.sh --stop
```

## 🗑️ Script Lama yang Digantikan

Script universal ini menggantikan:
- `startup.sh`
- `startup.ps1`
- `start-all.sh`
- `start-all.bat`
- `start-all-simple.bat`
- `quick-start.ps1`

**Rekomendasi**: Hapus script lama setelah memastikan script universal berfungsi dengan baik.

## 🔄 Migration dari Script Lama

| Script Lama | Command Baru |
|-------------|--------------|
| `./startup.sh` | `./start-atma.sh --staged` |
| `./start-all.sh` | `./start-atma.sh --simple` |
| `.\quick-start.ps1` | `.\start-atma.ps1 -Quick` |
| `.\quick-start.ps1 -Clean` | `.\start-atma.ps1 -Clean` |
| `start-all.bat` | `start-atma.bat --simple` |

## 📞 Support

Jika mengalami masalah:
1. Jalankan `./start-atma.sh --status` untuk melihat status layanan
2. Periksa log Docker: `docker-compose logs [service-name]`
3. Gunakan mode `--clean` untuk reset lengkap
4. Pastikan Docker dan Docker Compose versi terbaru
