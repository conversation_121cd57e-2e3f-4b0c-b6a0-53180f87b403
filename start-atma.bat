@echo off
REM ATMA Backend Universal Startup Script for Windows Batch
REM This script is a wrapper that calls the PowerShell script

setlocal enabledelayedexpansion

REM Check if PowerShell is available
where powershell >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: PowerShell is not available on this system
    echo Please use the PowerShell script directly: start-atma.ps1
    pause
    exit /b 1
)

REM Get the directory where this batch file is located
set "SCRIPT_DIR=%~dp0"

REM Build PowerShell command with all arguments
set "PS_ARGS="
set "HELP_REQUESTED=false"

REM Parse arguments
:parse_args
if "%~1"=="" goto :execute_ps
if /i "%~1"=="-h" set "HELP_REQUESTED=true"
if /i "%~1"=="--help" set "HELP_REQUESTED=true"
if /i "%~1"=="/?" set "HELP_REQUESTED=true"
if /i "%~1"=="help" set "HELP_REQUESTED=true"

REM Convert common arguments to PowerShell format
if /i "%~1"=="-q" set "PS_ARGS=!PS_ARGS! -Quick"
if /i "%~1"=="--quick" set "PS_ARGS=!PS_ARGS! -Quick"
if /i "%~1"=="-c" set "PS_ARGS=!PS_ARGS! -Clean"
if /i "%~1"=="--clean" set "PS_ARGS=!PS_ARGS! -Clean"
if /i "%~1"=="--no-pull" set "PS_ARGS=!PS_ARGS! -NoPull"
if /i "%~1"=="--no-cloudflare" set "PS_ARGS=!PS_ARGS! -NoCloudflare"
if /i "%~1"=="--no-docs" set "PS_ARGS=!PS_ARGS! -NoDocs"
if /i "%~1"=="--simple" set "PS_ARGS=!PS_ARGS! -Simple"
if /i "%~1"=="--staged" set "PS_ARGS=!PS_ARGS! -Staged"
if /i "%~1"=="--status" set "PS_ARGS=!PS_ARGS! -Status"
if /i "%~1"=="--stop" set "PS_ARGS=!PS_ARGS! -Stop"
if /i "%~1"=="--restart" set "PS_ARGS=!PS_ARGS! -Restart"

REM Handle arguments with values
echo %~1 | findstr /r "^--workers=" >nul
if !errorlevel! equ 0 (
    for /f "tokens=2 delims==" %%a in ("%~1") do set "PS_ARGS=!PS_ARGS! -Workers %%a"
)

echo %~1 | findstr /r "^--wait=" >nul
if !errorlevel! equ 0 (
    for /f "tokens=2 delims==" %%a in ("%~1") do set "PS_ARGS=!PS_ARGS! -Wait %%a"
)

shift
goto :parse_args

:execute_ps
REM Show help if requested
if "%HELP_REQUESTED%"=="true" (
    echo ATMA Backend Universal Startup Script - Batch Wrapper
    echo.
    echo This batch file is a wrapper for the PowerShell script.
    echo.
    echo Usage: start-atma.bat [OPTIONS]
    echo.
    echo Common Options:
    echo   -h, --help, /?          Show this help message
    echo   -q, --quick             Quick start ^(skip cleanup and pull^)
    echo   -c, --clean             Clean start ^(remove containers and images^)
    echo   --no-pull               Skip pulling latest images
    echo   --no-cloudflare         Skip Cloudflare tunnel
    echo   --no-docs               Skip documentation service
    echo   --workers=N             Number of analysis workers ^(default: 10^)
    echo   --wait=N                Wait time for services ^(default: 15 seconds^)
    echo   --simple                Simple mode ^(start all at once^)
    echo   --staged                Staged mode ^(start services in order^)
    echo   --status                Show current service status
    echo   --stop                  Stop all services
    echo   --restart               Restart all services
    echo.
    echo Examples:
    echo   start-atma.bat                    # Normal start
    echo   start-atma.bat --quick            # Quick start without cleanup
    echo   start-atma.bat --clean            # Clean start with full cleanup
    echo   start-atma.bat --workers=5        # Start with 5 analysis workers
    echo   start-atma.bat --simple           # Simple mode
    echo   start-atma.bat --status           # Check service status
    echo   start-atma.bat --stop             # Stop all services
    echo.
    echo For more advanced options, use the PowerShell script directly:
    echo   powershell -ExecutionPolicy Bypass -File start-atma.ps1 -Help
    echo.
    pause
    exit /b 0
)

REM Execute PowerShell script
echo Starting ATMA Backend...
echo Calling PowerShell script with arguments: %PS_ARGS%
echo.

powershell -ExecutionPolicy Bypass -File "%SCRIPT_DIR%start-atma.ps1" %PS_ARGS%

REM Check if PowerShell script succeeded
if %errorlevel% neq 0 (
    echo.
    echo ERROR: PowerShell script failed with exit code %errorlevel%
    echo.
    pause
    exit /b %errorlevel%
)

echo.
echo Batch script completed successfully.
pause
