#!/bin/bash

# ATMA Cloudflare Tunnel Fix Script
# This script helps diagnose and fix Cloudflare tunnel issues

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if .env file exists and has token
check_env_file() {
    print_status "Checking .env file..."
    
    if [ ! -f ".env" ]; then
        print_error ".env file not found!"
        print_status "Creating .env file template..."
        cat > .env << EOF
# Cloudflare Tunnel Token
# Dapatkan token ini dari Cloudflare Zero Trust Dashboard
CLOUDFLARE_TUNNEL_TOKEN=your_token_here
EOF
        print_warning "Please edit .env file and add your Cloudflare tunnel token"
        return 1
    fi
    
    if grep -q "CLOUDFLARE_TUNNEL_TOKEN=your_token_here" .env || ! grep -q "CLOUDFLARE_TUNNEL_TOKEN=" .env; then
        print_error "CLOUDFLARE_TUNNEL_TOKEN not properly set in .env file"
        print_status "Current .env content:"
        cat .env
        print_warning "Please set a valid Cloudflare tunnel token"
        return 1
    fi
    
    local token=$(grep "CLOUDFLARE_TUNNEL_TOKEN=" .env | cut -d'=' -f2)
    if [ -z "$token" ]; then
        print_error "CLOUDFLARE_TUNNEL_TOKEN is empty"
        return 1
    fi
    
    print_success ".env file looks good!"
    return 0
}

# Test Cloudflare tunnel token
test_tunnel_token() {
    print_status "Testing Cloudflare tunnel token..."
    
    local token=$(grep "CLOUDFLARE_TUNNEL_TOKEN=" .env | cut -d'=' -f2)
    
    # Try to run cloudflared with the token to validate it
    print_status "Attempting to validate token with cloudflared..."
    
    if docker run --rm cloudflare/cloudflared:latest tunnel --no-autoupdate --protocol http2 run --token "$token" --dry-run 2>/dev/null; then
        print_success "Cloudflare tunnel token is valid!"
        return 0
    else
        print_error "Cloudflare tunnel token validation failed!"
        print_status "This could mean:"
        print_status "1. Token is invalid or expired"
        print_status "2. Network connectivity issues"
        print_status "3. Cloudflare service issues"
        return 1
    fi
}

# Check if API Gateway is running
check_api_gateway() {
    print_status "Checking if API Gateway is running..."
    
    if docker-compose ps api-gateway | grep -q "Up"; then
        print_success "API Gateway is running!"
        return 0
    else
        print_error "API Gateway is not running!"
        print_status "Cloudflare tunnel requires API Gateway to be running first"
        return 1
    fi
}

# Start Cloudflare tunnel
start_tunnel() {
    print_status "Starting Cloudflare tunnel..."
    
    # Stop existing tunnel if running
    docker-compose stop cloudflared 2>/dev/null || true
    docker-compose rm -f cloudflared 2>/dev/null || true
    
    # Start tunnel
    if docker-compose up -d cloudflared; then
        print_success "Cloudflare tunnel started!"
        
        # Wait a bit and check status
        sleep 5
        if docker-compose ps cloudflared | grep -q "Up"; then
            print_success "Cloudflare tunnel is running!"
            
            # Show logs
            print_status "Recent tunnel logs:"
            docker-compose logs --tail=10 cloudflared
            return 0
        else
            print_error "Cloudflare tunnel failed to start!"
            print_status "Checking logs..."
            docker-compose logs cloudflared
            return 1
        fi
    else
        print_error "Failed to start Cloudflare tunnel!"
        return 1
    fi
}

# Show tunnel status and logs
show_tunnel_status() {
    print_status "=== Cloudflare Tunnel Status ==="
    
    if docker-compose ps cloudflared | grep -q "Up"; then
        print_success "Tunnel is running!"
        print_status "Container status:"
        docker-compose ps cloudflared
        
        print_status "Recent logs:"
        docker-compose logs --tail=20 cloudflared
    else
        print_warning "Tunnel is not running"
        print_status "Container status:"
        docker-compose ps cloudflared
        
        if docker-compose ps cloudflared | grep -q "Exit"; then
            print_status "Last logs before exit:"
            docker-compose logs --tail=20 cloudflared
        fi
    fi
}

# Main function
main() {
    print_status "🌐 ATMA Cloudflare Tunnel Diagnostic"
    print_status "===================================="
    
    case "$1" in
        "check")
            check_env_file && check_api_gateway && test_tunnel_token
            ;;
        "start")
            if check_env_file && check_api_gateway; then
                start_tunnel
            else
                print_error "Prerequisites not met. Run '$0 check' first."
                exit 1
            fi
            ;;
        "status")
            show_tunnel_status
            ;;
        "logs")
            print_status "Cloudflare tunnel logs:"
            docker-compose logs cloudflared
            ;;
        "restart")
            print_status "Restarting Cloudflare tunnel..."
            docker-compose restart cloudflared
            sleep 5
            show_tunnel_status
            ;;
        *)
            echo "Usage: $0 {check|start|status|logs|restart}"
            echo ""
            echo "Commands:"
            echo "  check    - Check prerequisites and token validity"
            echo "  start    - Start the Cloudflare tunnel"
            echo "  status   - Show tunnel status and recent logs"
            echo "  logs     - Show full tunnel logs"
            echo "  restart  - Restart the tunnel"
            echo ""
            echo "Examples:"
            echo "  $0 check     # Check if everything is configured correctly"
            echo "  $0 start     # Start the tunnel"
            echo "  $0 status    # Check current status"
            exit 1
            ;;
    esac
}

# Run main function
main "$1"
