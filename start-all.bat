@echo off
echo.
echo ========================================
echo    🚀 ATMA Backend Quick Start
echo ========================================
echo.

echo 📥 Pulling latest images...
docker-compose pull

echo.
echo 🏗️ Starting all services...
docker-compose up --build -d postgres redis rabbitmq auth-service assessment-service archive-service notification-service api-gateway testing-service analysis-worker-1 analysis-worker-2 analysis-worker-3 analysis-worker-4 analysis-worker-5 analysis-worker-6 analysis-worker-7 analysis-worker-8 analysis-worker-9 analysis-worker-10 documentation-service cloudflared

echo.
echo ⏳ Waiting for services to start...
timeout /t 15 /nobreak >nul

echo.
echo 📊 Service Status:
docker-compose ps

echo.
echo ========================================
echo    ✅ ATMA Backend Started!
echo ========================================
echo.
echo 🌐 Available Services:
echo    API Gateway: http://localhost:3000
echo    Auth Service: http://localhost:3001
echo    Archive Service: http://localhost:3002
echo    Assessment Service: http://localhost:3003
echo    Notification Service: http://localhost:3005
echo    Documentation: http://localhost:8080
echo    RabbitMQ: http://localhost:15672 (admin/admin123)
echo.
echo 🧪 To run tests:
echo    node testing/e2e-test.js
echo    node testing/load-test.js
echo.
echo 🔧 To check status:
echo    docker-compose ps
echo.
pause
