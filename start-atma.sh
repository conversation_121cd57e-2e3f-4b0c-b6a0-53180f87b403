#!/bin/bash

# ATMA Backend Universal Startup Script
# Compatible with Linux, macOS, and Windows (via Git Bash/WSL)
# This script replaces all other startup scripts

set -e

# Script version and info
SCRIPT_VERSION="1.0.0"
SCRIPT_NAME="ATMA Universal Startup"

# Default configuration
DEFAULT_WORKER_COUNT=10
DEFAULT_WAIT_TIME=15
DEFAULT_MAX_ATTEMPTS=30

# Colors for output
if [[ -t 1 ]]; then
    RED='\033[0;31m'
    GREEN='\033[0;32m'
    YELLOW='\033[1;33m'
    BLUE='\033[0;34m'
    CYAN='\033[0;36m'
    MAGENTA='\033[0;35m'
    NC='\033[0m'
else
    RED=''
    GREEN=''
    YELLOW=''
    BLUE=''
    CYAN=''
    MAGENTA=''
    NC=''
fi

# Function to print colored output
print_header() {
    echo -e "${GREEN}"
    echo "========================================"
    echo "    🚀 $1"
    echo "========================================"
    echo -e "${NC}"
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${CYAN}[STEP]${NC} $1"
}

# Function to show help
show_help() {
    echo -e "${GREEN}$SCRIPT_NAME v$SCRIPT_VERSION${NC}"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -h, --help              Show this help message"
    echo "  -v, --version           Show version information"
    echo "  -q, --quick             Quick start (skip cleanup and pull)"
    echo "  -c, --clean             Clean start (remove containers and images)"
    echo "  --no-pull               Skip pulling latest images"
    echo "  --no-cloudflare         Skip Cloudflare tunnel"
    echo "  --no-docs               Skip documentation service"
    echo "  --workers=N             Number of analysis workers (default: $DEFAULT_WORKER_COUNT)"
    echo "  --wait=N                Wait time for services (default: $DEFAULT_WAIT_TIME seconds)"
    echo "  --simple                Simple mode (start all at once, no health checks)"
    echo "  --staged                Staged mode (start services in order with health checks)"
    echo "  --status                Show current service status"
    echo "  --stop                  Stop all services"
    echo "  --restart               Restart all services"
    echo ""
    echo "Examples:"
    echo "  $0                      # Normal start"
    echo "  $0 --quick              # Quick start without cleanup"
    echo "  $0 --clean              # Clean start with full cleanup"
    echo "  $0 --workers=5          # Start with 5 analysis workers"
    echo "  $0 --simple             # Simple mode (faster but less reliable)"
    echo "  $0 --status             # Check service status"
    echo "  $0 --stop               # Stop all services"
    echo ""
}

# Function to check if Docker is running
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed or not in PATH"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        print_error "Docker is not running. Please start Docker first."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed or not in PATH"
        exit 1
    fi
}

# Function to wait for service to be healthy
wait_for_service() {
    local service_name=$1
    local max_attempts=${2:-$DEFAULT_MAX_ATTEMPTS}
    local attempt=1
    
    print_status "Waiting for $service_name to be healthy..."
    
    while [ $attempt -le $max_attempts ]; do
        local status=$(docker-compose ps $service_name 2>/dev/null || echo "")
        
        if echo "$status" | grep -q "healthy"; then
            print_success "$service_name is healthy!"
            return 0
        fi
        
        if echo "$status" | grep -q "unhealthy"; then
            print_warning "$service_name is unhealthy, attempt $attempt/$max_attempts"
        elif echo "$status" | grep -q "Up"; then
            print_status "$service_name is starting, attempt $attempt/$max_attempts"
        else
            print_status "$service_name not yet started, attempt $attempt/$max_attempts"
        fi
        
        sleep 2
        attempt=$((attempt + 1))
    done
    
    print_error "$service_name failed to become healthy after $max_attempts attempts"
    return 1
}

# Function to start services and wait for them
start_and_wait() {
    local services="$1"
    local skip_health_check=${2:-false}
    
    print_step "Starting: $services"
    
    if ! docker-compose up --build -d $services; then
        print_error "Failed to start services: $services"
        return 1
    fi
    
    if [ "$skip_health_check" = "false" ]; then
        # Wait for each service to be healthy
        for service in $services; do
            if ! wait_for_service $service; then
                print_warning "$service failed to become healthy, but continuing..."
            fi
        done
    fi
}

# Function to show service status
show_status() {
    print_header "Service Status"
    docker-compose ps
    echo ""
    
    print_status "Service URLs:"
    echo -e "${CYAN}🌐 API Gateway:${NC} http://localhost:3000"
    echo -e "${CYAN}🔐 Auth Service:${NC} http://localhost:3001"
    echo -e "${CYAN}📁 Archive Service:${NC} http://localhost:3002"
    echo -e "${CYAN}📝 Assessment Service:${NC} http://localhost:3003"
    echo -e "${CYAN}🔔 Notification Service:${NC} http://localhost:3005"
    echo -e "${CYAN}📚 Documentation:${NC} http://localhost:8080"
    echo -e "${CYAN}🐰 RabbitMQ:${NC} http://localhost:15672 (admin/admin123)"
    echo ""
}

# Function to stop all services
stop_services() {
    print_header "Stopping ATMA Services"
    print_status "Stopping all containers..."
    docker-compose down --volumes --remove-orphans
    print_success "All services stopped!"
}

# Function to clean Docker system
clean_docker() {
    print_status "Cleaning Docker system..."
    docker-compose down --volumes --remove-orphans 2>/dev/null || true
    docker system prune -a --volumes -f
    print_success "Docker system cleaned!"
}

# Function to start services in simple mode
start_simple() {
    local worker_count=$1
    local skip_pull=$2
    local skip_cloudflare=$3
    local skip_docs=$4
    
    print_header "ATMA Simple Start Mode"
    
    if [ "$skip_pull" = "false" ]; then
        print_status "📥 Pulling latest images..."
        docker-compose pull
    fi
    
    print_status "🏗️ Starting all services..."
    
    # Build service list
    local services="postgres redis rabbitmq auth-service assessment-service archive-service notification-service api-gateway testing-service"
    
    # Add analysis workers
    for i in $(seq 1 $worker_count); do
        services="$services analysis-worker-$i"
    done
    
    # Add optional services
    if [ "$skip_docs" = "false" ]; then
        services="$services documentation-service"
    fi
    
    if [ "$skip_cloudflare" = "false" ]; then
        services="$services cloudflared"
    fi
    
    # Start all services at once
    docker-compose up --build -d $services
    
    print_status "⏳ Waiting for services to start..."
    sleep $DEFAULT_WAIT_TIME
    
    show_status
    print_success "🎉 ATMA Backend started in simple mode!"
}

# Function to start services in staged mode
start_staged() {
    local worker_count=$1
    local skip_pull=$2
    local skip_cloudflare=$3
    local skip_docs=$4
    
    print_header "ATMA Staged Start Mode"
    
    if [ "$skip_pull" = "false" ]; then
        print_status "📥 Pulling latest images..."
        docker-compose pull
    fi
    
    print_status "🏗️ Starting services in stages..."
    
    # Stage 1: Infrastructure services
    print_step "=== Stage 1: Infrastructure Services ==="
    start_and_wait "postgres redis rabbitmq"
    
    # Stage 2: Core application services
    print_step "=== Stage 2: Core Application Services ==="
    start_and_wait "auth-service"
    start_and_wait "assessment-service archive-service notification-service"
    
    # Stage 3: API Gateway
    print_step "=== Stage 3: API Gateway ==="
    start_and_wait "api-gateway"
    
    # Stage 4: Analysis Workers
    print_step "=== Stage 4: Analysis Workers ==="
    local workers=""
    for i in $(seq 1 $worker_count); do
        workers="$workers analysis-worker-$i"
    done
    docker-compose up --build -d $workers
    
    # Wait for workers (but don't fail if some don't start)
    for i in $(seq 1 $worker_count); do
        wait_for_service "analysis-worker-$i" 10 || print_warning "analysis-worker-$i failed to start"
    done
    
    # Stage 5: Testing service
    print_step "=== Stage 5: Testing Service ==="
    start_and_wait "testing-service"
    
    # Stage 6: Documentation service (optional)
    if [ "$skip_docs" = "false" ]; then
        print_step "=== Stage 6: Documentation Service ==="
        start_and_wait "documentation-service" || print_warning "Documentation service failed to start"
    fi
    
    # Stage 7: Cloudflare tunnel (optional)
    if [ "$skip_cloudflare" = "false" ]; then
        print_step "=== Stage 7: Cloudflare Tunnel ==="
        if [ -f ".env" ] && grep -q "CLOUDFLARE_TUNNEL_TOKEN=" .env; then
            local token=$(grep "CLOUDFLARE_TUNNEL_TOKEN=" .env | cut -d'=' -f2)
            if [ "$token" != "your_token_here" ] && [ -n "$token" ]; then
                print_status "Starting Cloudflare tunnel..."
                docker-compose up -d cloudflared || print_warning "Cloudflare tunnel failed to start (optional)"
            else
                print_warning "CLOUDFLARE_TUNNEL_TOKEN not properly set, skipping"
            fi
        else
            print_warning "No .env file or CLOUDFLARE_TUNNEL_TOKEN not found, skipping Cloudflare tunnel"
        fi
    fi
    
    show_status
    print_success "🎉 ATMA Backend started in staged mode!"
}

# Parse command line arguments
QUICK_MODE=false
CLEAN_MODE=false
SKIP_PULL=false
SKIP_CLOUDFLARE=false
SKIP_DOCS=false
WORKER_COUNT=$DEFAULT_WORKER_COUNT
WAIT_TIME=$DEFAULT_WAIT_TIME
SIMPLE_MODE=false
STAGED_MODE=false
SHOW_STATUS=false
STOP_SERVICES=false
RESTART_SERVICES=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -v|--version)
            echo "$SCRIPT_NAME v$SCRIPT_VERSION"
            exit 0
            ;;
        -q|--quick)
            QUICK_MODE=true
            shift
            ;;
        -c|--clean)
            CLEAN_MODE=true
            shift
            ;;
        --no-pull)
            SKIP_PULL=true
            shift
            ;;
        --no-cloudflare)
            SKIP_CLOUDFLARE=true
            shift
            ;;
        --no-docs)
            SKIP_DOCS=true
            shift
            ;;
        --workers=*)
            WORKER_COUNT="${1#*=}"
            shift
            ;;
        --wait=*)
            WAIT_TIME="${1#*=}"
            shift
            ;;
        --simple)
            SIMPLE_MODE=true
            shift
            ;;
        --staged)
            STAGED_MODE=true
            shift
            ;;
        --status)
            SHOW_STATUS=true
            shift
            ;;
        --stop)
            STOP_SERVICES=true
            shift
            ;;
        --restart)
            RESTART_SERVICES=true
            shift
            ;;
        *)
            print_error "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Main execution
main() {
    # Check Docker availability
    check_docker
    
    # Handle special commands
    if [ "$SHOW_STATUS" = "true" ]; then
        show_status
        exit 0
    fi
    
    if [ "$STOP_SERVICES" = "true" ]; then
        stop_services
        exit 0
    fi
    
    if [ "$RESTART_SERVICES" = "true" ]; then
        stop_services
        sleep 2
        # Continue to start services
    fi
    
    # Set defaults based on mode
    if [ "$QUICK_MODE" = "true" ]; then
        SKIP_PULL=true
        CLEAN_MODE=false
    fi
    
    # Default to staged mode if no mode specified
    if [ "$SIMPLE_MODE" = "false" ] && [ "$STAGED_MODE" = "false" ]; then
        STAGED_MODE=true
    fi
    
    # Clean Docker if requested
    if [ "$CLEAN_MODE" = "true" ]; then
        clean_docker
    fi
    
    # Start services based on mode
    if [ "$SIMPLE_MODE" = "true" ]; then
        start_simple $WORKER_COUNT $SKIP_PULL $SKIP_CLOUDFLARE $SKIP_DOCS
    else
        start_staged $WORKER_COUNT $SKIP_PULL $SKIP_CLOUDFLARE $SKIP_DOCS
    fi
    
    # Show final information
    echo ""
    print_status "🧪 To run tests:"
    echo "   ./run-tests.sh"
    echo "   node testing/e2e-test.js"
    echo "   node testing/load-test.js"
    echo ""
    print_status "🔧 Useful commands:"
    echo "   $0 --status     # Check service status"
    echo "   $0 --stop       # Stop all services"
    echo "   $0 --restart    # Restart all services"
    echo ""
}

# Run main function
main "$@"
