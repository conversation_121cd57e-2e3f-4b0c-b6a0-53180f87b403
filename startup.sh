#!/bin/bash

# ATMA Backend Startup Script
# This script ensures all services start in the correct order

set -e

echo "🚀 Starting ATMA Backend Services..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to wait for service to be healthy
wait_for_service() {
    local service_name=$1
    local max_attempts=30
    local attempt=1
    
    print_status "Waiting for $service_name to be healthy..."
    
    while [ $attempt -le $max_attempts ]; do
        if docker-compose ps $service_name | grep -q "healthy"; then
            print_success "$service_name is healthy!"
            return 0
        fi
        
        if docker-compose ps $service_name | grep -q "unhealthy"; then
            print_warning "$service_name is unhealthy, attempt $attempt/$max_attempts"
        else
            print_status "$service_name starting, attempt $attempt/$max_attempts"
        fi
        
        sleep 2
        attempt=$((attempt + 1))
    done
    
    print_error "$service_name failed to become healthy after $max_attempts attempts"
    return 1
}

# Function to start services and wait for them
start_and_wait() {
    local services="$1"
    print_status "Starting: $services"
    
    docker-compose up --build -d $services
    
    # Wait for each service to be healthy
    for service in $services; do
        wait_for_service $service
    done
}

# Clean up any existing containers
print_status "Cleaning up existing containers..."
docker-compose down --volumes --remove-orphans 2>/dev/null || true

# Remove old images and clean system
print_status "Cleaning Docker system..."
docker system prune -a --volumes -f

# Pull latest images
print_status "Pulling latest images..."
docker-compose pull

print_status "Starting services in correct order..."

# Stage 1: Infrastructure services
print_status "=== Stage 1: Infrastructure Services ==="
start_and_wait "postgres redis rabbitmq"

# Stage 2: Core application services
print_status "=== Stage 2: Core Application Services ==="
start_and_wait "auth-service"
start_and_wait "assessment-service archive-service notification-service"

# Stage 3: API Gateway
print_status "=== Stage 3: API Gateway ==="
start_and_wait "api-gateway"

# Stage 4: Analysis Workers (all at once since they depend on the same services)
print_status "=== Stage 4: Analysis Workers ==="
docker-compose up --build -d \
    analysis-worker-1 analysis-worker-2 analysis-worker-3 analysis-worker-4 analysis-worker-5 \
    analysis-worker-6 analysis-worker-7 analysis-worker-8 analysis-worker-9 analysis-worker-10

# Wait for workers to be healthy
for i in {1..10}; do
    wait_for_service "analysis-worker-$i"
done

# Stage 5: Testing service
print_status "=== Stage 5: Testing Service ==="
start_and_wait "testing-service"

# Stage 6: Documentation service
print_status "=== Stage 6: Documentation Service ==="
start_and_wait "documentation-service"

# Stage 7: Cloudflare tunnel (optional)
print_status "=== Stage 7: Cloudflare Tunnel ==="
if [ ! -z "$CLOUDFLARE_TUNNEL_TOKEN" ]; then
    print_status "Starting Cloudflare tunnel..."
    docker-compose up -d cloudflared || print_warning "Cloudflare tunnel failed to start (this is optional)"
else
    print_warning "CLOUDFLARE_TUNNEL_TOKEN not set, skipping Cloudflare tunnel"
fi

# Final status check
print_status "=== Final Status Check ==="
docker-compose ps

print_success "🎉 ATMA Backend startup completed!"
print_status "Services are now running and ready for testing."
print_status ""
print_status "Available endpoints:"
print_status "- API Gateway: http://localhost:3000"
print_status "- Auth Service: http://localhost:3001"
print_status "- Archive Service: http://localhost:3002"
print_status "- Assessment Service: http://localhost:3003"
print_status "- Notification Service: http://localhost:3005"
print_status "- RabbitMQ Management: http://localhost:15672 (admin/admin123)"
print_status ""
print_status "To run tests:"
print_status "- E2E Test: node testing/e2e-test.js"
print_status "- Load Test: node testing/load-test.js"
