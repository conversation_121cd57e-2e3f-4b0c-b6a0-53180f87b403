# ATMA Cloudflare Tunnel Fix Script (PowerShell)
# This script helps diagnose and fix Cloudflare tunnel issues

param(
    [string]$Action = "help"
)

# Function to print colored output
function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# Check if .env file exists and has token
function Test-EnvFile {
    Write-Status "Checking .env file..."
    
    if (!(Test-Path ".env")) {
        Write-Error ".env file not found!"
        Write-Status "Creating .env file template..."
        @"
# Cloudflare Tunnel Token
# Dapatkan token ini dari Cloudflare Zero Trust Dashboard
CLOUDFLARE_TUNNEL_TOKEN=your_token_here
"@ | Out-File -FilePath ".env" -Encoding UTF8
        Write-Warning "Please edit .env file and add your Cloudflare tunnel token"
        return $false
    }
    
    $envContent = Get-Content ".env" -ErrorAction SilentlyContinue
    $tokenLine = $envContent | Where-Object { $_ -match "CLOUDFLARE_TUNNEL_TOKEN=" }
    
    if (!$tokenLine -or $tokenLine -match "your_token_here") {
        Write-Error "CLOUDFLARE_TUNNEL_TOKEN not properly set in .env file"
        Write-Status "Current .env content:"
        Get-Content ".env"
        Write-Warning "Please set a valid Cloudflare tunnel token"
        return $false
    }
    
    $token = ($tokenLine -split "=", 2)[1]
    if ([string]::IsNullOrWhiteSpace($token)) {
        Write-Error "CLOUDFLARE_TUNNEL_TOKEN is empty"
        return $false
    }
    
    Write-Success ".env file looks good!"
    return $true
}

# Check if API Gateway is running
function Test-ApiGateway {
    Write-Status "Checking if API Gateway is running..."
    
    $status = docker-compose ps api-gateway
    if ($status -match "Up") {
        Write-Success "API Gateway is running!"
        return $true
    } else {
        Write-Error "API Gateway is not running!"
        Write-Status "Cloudflare tunnel requires API Gateway to be running first"
        return $false
    }
}

# Start Cloudflare tunnel
function Start-Tunnel {
    Write-Status "Starting Cloudflare tunnel..."
    
    # Stop existing tunnel if running
    try {
        docker-compose stop cloudflared 2>$null
        docker-compose rm -f cloudflared 2>$null
    } catch {
        # Ignore errors
    }
    
    # Start tunnel
    try {
        docker-compose up -d cloudflared
        Write-Success "Cloudflare tunnel started!"
        
        # Wait a bit and check status
        Start-Sleep -Seconds 5
        $status = docker-compose ps cloudflared
        if ($status -match "Up") {
            Write-Success "Cloudflare tunnel is running!"
            
            # Show logs
            Write-Status "Recent tunnel logs:"
            docker-compose logs --tail=10 cloudflared
            return $true
        } else {
            Write-Error "Cloudflare tunnel failed to start!"
            Write-Status "Checking logs..."
            docker-compose logs cloudflared
            return $false
        }
    } catch {
        Write-Error "Failed to start Cloudflare tunnel: $_"
        return $false
    }
}

# Show tunnel status and logs
function Show-TunnelStatus {
    Write-Status "=== Cloudflare Tunnel Status ==="
    
    $status = docker-compose ps cloudflared
    if ($status -match "Up") {
        Write-Success "Tunnel is running!"
        Write-Status "Container status:"
        docker-compose ps cloudflared
        
        Write-Status "Recent logs:"
        docker-compose logs --tail=20 cloudflared
    } else {
        Write-Warning "Tunnel is not running"
        Write-Status "Container status:"
        docker-compose ps cloudflared
        
        if ($status -match "Exit") {
            Write-Status "Last logs before exit:"
            docker-compose logs --tail=20 cloudflared
        }
    }
}

# Main function
try {
    Write-Status "🌐 ATMA Cloudflare Tunnel Diagnostic"
    Write-Status "===================================="
    
    switch ($Action.ToLower()) {
        "check" {
            $envOk = Test-EnvFile
            $gatewayOk = Test-ApiGateway
            
            if ($envOk -and $gatewayOk) {
                Write-Success "All prerequisites met!"
                exit 0
            } else {
                Write-Error "Prerequisites not met"
                exit 1
            }
        }
        "start" {
            if ((Test-EnvFile) -and (Test-ApiGateway)) {
                if (Start-Tunnel) {
                    exit 0
                } else {
                    exit 1
                }
            } else {
                Write-Error "Prerequisites not met. Run 'fix-cloudflare.ps1 check' first."
                exit 1
            }
        }
        "status" {
            Show-TunnelStatus
        }
        "logs" {
            Write-Status "Cloudflare tunnel logs:"
            docker-compose logs cloudflared
        }
        "restart" {
            Write-Status "Restarting Cloudflare tunnel..."
            docker-compose restart cloudflared
            Start-Sleep -Seconds 5
            Show-TunnelStatus
        }
        default {
            Write-Host "Usage: .\fix-cloudflare.ps1 <action>"
            Write-Host ""
            Write-Host "Actions:"
            Write-Host "  check    - Check prerequisites and token validity"
            Write-Host "  start    - Start the Cloudflare tunnel"
            Write-Host "  status   - Show tunnel status and recent logs"
            Write-Host "  logs     - Show full tunnel logs"
            Write-Host "  restart  - Restart the tunnel"
            Write-Host ""
            Write-Host "Examples:"
            Write-Host "  .\fix-cloudflare.ps1 check     # Check if everything is configured correctly"
            Write-Host "  .\fix-cloudflare.ps1 start     # Start the tunnel"
            Write-Host "  .\fix-cloudflare.ps1 status    # Check current status"
            exit 1
        }
    }
} catch {
    Write-Error "Script failed: $_"
    exit 1
}
