# ATMA Quick Start Script
# <PERSON><PERSON><PERSON>an semua services dengan satu perintah

param(
    [switch]$Clean,
    [switch]$NoPull,
    [switch]$NoCloudflare
)

Write-Host "🚀 ATMA Quick Start" -ForegroundColor Green
Write-Host "==================" -ForegroundColor Green

# Clean up if requested
if ($Clean) {
    Write-Host "🧹 Cleaning up..." -ForegroundColor Yellow
    docker-compose down --volumes --remove-orphans 2>$null
    docker system prune -a --volumes -f
}

# Pull images if not skipped
if (!$NoPull) {
    Write-Host "📥 Pulling latest images..." -ForegroundColor Blue
    docker-compose pull
}

Write-Host "🏗️ Starting all services..." -ForegroundColor Blue

# Start all services at once
$services = @(
    "postgres", "redis", "rabbitmq",
    "auth-service", "assessment-service", "archive-service", "notification-service",
    "api-gateway", "testing-service",
    "analysis-worker-1", "analysis-worker-2", "analysis-worker-3", "analysis-worker-4", "analysis-worker-5",
    "analysis-worker-6", "analysis-worker-7", "analysis-worker-8", "analysis-worker-9", "analysis-worker-10"
)

if (!$NoCloudflare) {
    $services += "documentation-service"
    $services += "cloudflared"
}

try {
    docker-compose up --build -d $services

    Write-Host "⏳ Waiting for services to start..." -ForegroundColor Yellow
    Start-Sleep -Seconds 15

    Write-Host "📊 Service Status:" -ForegroundColor Green
    docker-compose ps

    Write-Host ""
    Write-Host "✅ ATMA Backend Started!" -ForegroundColor Green
    Write-Host "========================" -ForegroundColor Green
    Write-Host "🌐 API Gateway: http://localhost:3000" -ForegroundColor Cyan
    Write-Host "🔐 Auth Service: http://localhost:3001" -ForegroundColor Cyan
    Write-Host "📁 Archive Service: http://localhost:3002" -ForegroundColor Cyan
    Write-Host "📝 Assessment Service: http://localhost:3003" -ForegroundColor Cyan
    Write-Host "🔔 Notification Service: http://localhost:3005" -ForegroundColor Cyan
    Write-Host "📚 Documentation: http://localhost:8080" -ForegroundColor Cyan
    Write-Host "🐰 RabbitMQ: http://localhost:15672 (admin/admin123)" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "🧪 To run tests:" -ForegroundColor Yellow
    Write-Host "   .\run-tests.ps1" -ForegroundColor White
    Write-Host ""
    Write-Host "🔧 To check Cloudflare:" -ForegroundColor Yellow
    Write-Host "   .\fix-cloudflare.ps1 status" -ForegroundColor White

    Write-Host ""
    Write-Host "💡 Usage Examples:" -ForegroundColor Magenta
    Write-Host "   .\quick-start.ps1                    # Normal start" -ForegroundColor White
    Write-Host "   .\quick-start.ps1 -Clean             # Clean start (remove old containers)" -ForegroundColor White
    Write-Host "   .\quick-start.ps1 -NoPull            # Skip pulling images" -ForegroundColor White
    Write-Host "   .\quick-start.ps1 -NoCloudflare      # Skip Cloudflare tunnel" -ForegroundColor White

} catch {
    Write-Host "❌ Failed to start services: $_" -ForegroundColor Red
    Write-Host "📋 Current status:" -ForegroundColor Yellow
    docker-compose ps
    exit 1
}
