# ATMA Backend Startup Script (PowerShell)
# This script ensures all services start in the correct order

param(
    [switch]$SkipCleanup,
    [switch]$SkipPull
)

# Function to print colored output
function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# Function to wait for service to be healthy
function Wait-ForService {
    param(
        [string]$ServiceName,
        [int]$MaxAttempts = 30
    )
    
    Write-Status "Waiting for $ServiceName to be healthy..."
    
    for ($attempt = 1; $attempt -le $MaxAttempts; $attempt++) {
        $status = docker-compose ps $ServiceName
        
        if ($status -match "healthy") {
            Write-Success "$ServiceName is healthy!"
            return $true
        }
        
        if ($status -match "unhealthy") {
            Write-Warning "$ServiceName is unhealthy, attempt $attempt/$MaxAttempts"
        } else {
            Write-Status "$ServiceName starting, attempt $attempt/$MaxAttempts"
        }
        
        Start-Sleep -Seconds 2
    }
    
    Write-Error "$ServiceName failed to become healthy after $MaxAttempts attempts"
    return $false
}

# Function to start services and wait for them
function Start-AndWait {
    param([string]$Services)
    
    Write-Status "Starting: $Services"
    docker-compose up --build -d $Services.Split(' ')
    
    # Wait for each service to be healthy
    foreach ($service in $Services.Split(' ')) {
        if (!(Wait-ForService -ServiceName $service)) {
            throw "Failed to start $service"
        }
    }
}

try {
    Write-Status "🚀 Starting ATMA Backend Services..."

    # Clean up any existing containers (unless skipped)
    if (!$SkipCleanup) {
        Write-Status "Cleaning up existing containers..."
        docker-compose down --volumes --remove-orphans 2>$null
        
        Write-Status "Cleaning Docker system..."
        docker system prune -a --volumes -f
    }

    # Pull latest images (unless skipped)
    if (!$SkipPull) {
        Write-Status "Pulling latest images..."
        docker-compose pull
    }

    Write-Status "Starting services in correct order..."

    # Stage 1: Infrastructure services
    Write-Status "=== Stage 1: Infrastructure Services ==="
    Start-AndWait "postgres redis rabbitmq"

    # Stage 2: Core application services
    Write-Status "=== Stage 2: Core Application Services ==="
    Start-AndWait "auth-service"
    Start-AndWait "assessment-service archive-service notification-service"

    # Stage 3: API Gateway
    Write-Status "=== Stage 3: API Gateway ==="
    Start-AndWait "api-gateway"

    # Stage 4: Analysis Workers
    Write-Status "=== Stage 4: Analysis Workers ==="
    docker-compose up --build -d analysis-worker-1 analysis-worker-2 analysis-worker-3 analysis-worker-4 analysis-worker-5 analysis-worker-6 analysis-worker-7 analysis-worker-8 analysis-worker-9 analysis-worker-10

    # Wait for workers to be healthy
    for ($i = 1; $i -le 10; $i++) {
        if (!(Wait-ForService -ServiceName "analysis-worker-$i")) {
            Write-Warning "analysis-worker-$i failed to start, but continuing..."
        }
    }

    # Stage 5: Testing service
    Write-Status "=== Stage 5: Testing Service ==="
    Start-AndWait "testing-service"

    # Stage 6: Documentation service
    Write-Status "=== Stage 6: Documentation Service ==="
    try {
        Start-AndWait "documentation-service"
    } catch {
        Write-Warning "Documentation service failed to start: $_"
    }

    # Stage 7: Cloudflare tunnel (optional)
    Write-Status "=== Stage 7: Cloudflare Tunnel ==="
    $cloudflareToken = (Get-Content .env -ErrorAction SilentlyContinue | Where-Object { $_ -match "CLOUDFLARE_TUNNEL_TOKEN=" }) -replace "CLOUDFLARE_TUNNEL_TOKEN=", ""
    
    if ($cloudflareToken -and $cloudflareToken -ne "your_token_here") {
        Write-Status "Starting Cloudflare tunnel..."
        try {
            docker-compose up -d cloudflared
            Write-Success "Cloudflare tunnel started"
        } catch {
            Write-Warning "Cloudflare tunnel failed to start (this is optional): $_"
        }
    } else {
        Write-Warning "CLOUDFLARE_TUNNEL_TOKEN not properly set, skipping Cloudflare tunnel"
    }

    # Final status check
    Write-Status "=== Final Status Check ==="
    docker-compose ps

    Write-Success "🎉 ATMA Backend startup completed!"
    Write-Status "Services are now running and ready for testing."
    Write-Status ""
    Write-Status "Available endpoints:"
    Write-Status "- API Gateway: http://localhost:3000"
    Write-Status "- Auth Service: http://localhost:3001"
    Write-Status "- Archive Service: http://localhost:3002"
    Write-Status "- Assessment Service: http://localhost:3003"
    Write-Status "- Notification Service: http://localhost:3005"
    Write-Status "- RabbitMQ Management: http://localhost:15672 (admin/admin123)"
    Write-Status ""
    Write-Status "To run tests:"
    Write-Status "- E2E Test: node testing/e2e-test.js"
    Write-Status "- Load Test: node testing/load-test.js"
    Write-Status "- Or use: .\run-tests.ps1"

} catch {
    Write-Error "Startup failed: $_"
    Write-Status "Checking service status..."
    docker-compose ps
    exit 1
}
