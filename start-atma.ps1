# ATMA Backend Universal Startup Script for Windows PowerShell
# This script replaces all other startup scripts

param(
    [switch]$Help,
    [switch]$Version,
    [switch]$Quick,
    [switch]$Clean,
    [switch]$NoPull,
    [switch]$NoCloudflare,
    [switch]$NoDocs,
    [int]$Workers = 10,
    [int]$Wait = 15,
    [switch]$Simple,
    [switch]$Staged,
    [switch]$Status,
    [switch]$Stop,
    [switch]$Restart
)

# Script version and info
$SCRIPT_VERSION = "1.0.0"
$SCRIPT_NAME = "ATMA Universal Startup"

# Function to print colored output
function Write-Header {
    param([string]$Message)
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "    🚀 $Message" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Green
    Write-Host ""
}

function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

function Write-Step {
    param([string]$Message)
    Write-Host "[STEP] $Message" -ForegroundColor Cyan
}

# Function to show help
function Show-Help {
    Write-Host "$SCRIPT_NAME v$SCRIPT_VERSION" -ForegroundColor Green
    Write-Host ""
    Write-Host "Usage: .\start-atma.ps1 [OPTIONS]"
    Write-Host ""
    Write-Host "Options:"
    Write-Host "  -Help                   Show this help message"
    Write-Host "  -Version                Show version information"
    Write-Host "  -Quick                  Quick start (skip cleanup and pull)"
    Write-Host "  -Clean                  Clean start (remove containers and images)"
    Write-Host "  -NoPull                 Skip pulling latest images"
    Write-Host "  -NoCloudflare           Skip Cloudflare tunnel"
    Write-Host "  -NoDocs                 Skip documentation service"
    Write-Host "  -Workers N              Number of analysis workers (default: 10)"
    Write-Host "  -Wait N                 Wait time for services (default: 15 seconds)"
    Write-Host "  -Simple                 Simple mode (start all at once, no health checks)"
    Write-Host "  -Staged                 Staged mode (start services in order with health checks)"
    Write-Host "  -Status                 Show current service status"
    Write-Host "  -Stop                   Stop all services"
    Write-Host "  -Restart                Restart all services"
    Write-Host ""
    Write-Host "Examples:"
    Write-Host "  .\start-atma.ps1                    # Normal start"
    Write-Host "  .\start-atma.ps1 -Quick             # Quick start without cleanup"
    Write-Host "  .\start-atma.ps1 -Clean             # Clean start with full cleanup"
    Write-Host "  .\start-atma.ps1 -Workers 5         # Start with 5 analysis workers"
    Write-Host "  .\start-atma.ps1 -Simple            # Simple mode (faster but less reliable)"
    Write-Host "  .\start-atma.ps1 -Status            # Check service status"
    Write-Host "  .\start-atma.ps1 -Stop              # Stop all services"
    Write-Host ""
}

# Function to check if Docker is running
function Test-Docker {
    try {
        if (!(Get-Command docker -ErrorAction SilentlyContinue)) {
            Write-Error "Docker is not installed or not in PATH"
            exit 1
        }
        
        $null = docker info 2>$null
        if ($LASTEXITCODE -ne 0) {
            Write-Error "Docker is not running. Please start Docker first."
            exit 1
        }
        
        if (!(Get-Command docker-compose -ErrorAction SilentlyContinue)) {
            Write-Error "Docker Compose is not installed or not in PATH"
            exit 1
        }
    }
    catch {
        Write-Error "Failed to check Docker: $_"
        exit 1
    }
}

# Function to wait for service to be healthy
function Wait-ForService {
    param(
        [string]$ServiceName,
        [int]$MaxAttempts = 30
    )
    
    Write-Status "Waiting for $ServiceName to be healthy..."
    
    for ($attempt = 1; $attempt -le $MaxAttempts; $attempt++) {
        try {
            $status = docker-compose ps $ServiceName 2>$null
            
            if ($status -match "healthy") {
                Write-Success "$ServiceName is healthy!"
                return $true
            }
            
            if ($status -match "unhealthy") {
                Write-Warning "$ServiceName is unhealthy, attempt $attempt/$MaxAttempts"
            }
            elseif ($status -match "Up") {
                Write-Status "$ServiceName is starting, attempt $attempt/$MaxAttempts"
            }
            else {
                Write-Status "$ServiceName not yet started, attempt $attempt/$MaxAttempts"
            }
        }
        catch {
            Write-Status "$ServiceName status check failed, attempt $attempt/$MaxAttempts"
        }
        
        Start-Sleep -Seconds 2
    }
    
    Write-Error "$ServiceName failed to become healthy after $MaxAttempts attempts"
    return $false
}

# Function to start services and wait for them
function Start-AndWait {
    param(
        [string[]]$Services,
        [bool]$SkipHealthCheck = $false
    )
    
    $serviceList = $Services -join " "
    Write-Step "Starting: $serviceList"
    
    try {
        docker-compose up --build -d $Services
        if ($LASTEXITCODE -ne 0) {
            Write-Error "Failed to start services: $serviceList"
            return $false
        }
    }
    catch {
        Write-Error "Failed to start services: $serviceList - $_"
        return $false
    }
    
    if (-not $SkipHealthCheck) {
        # Wait for each service to be healthy
        foreach ($service in $Services) {
            if (!(Wait-ForService -ServiceName $service)) {
                Write-Warning "$service failed to become healthy, but continuing..."
            }
        }
    }
    
    return $true
}

# Function to show service status
function Show-Status {
    Write-Header "Service Status"
    docker-compose ps
    Write-Host ""
    
    Write-Status "Service URLs:"
    Write-Host "🌐 API Gateway: http://localhost:3000" -ForegroundColor Cyan
    Write-Host "🔐 Auth Service: http://localhost:3001" -ForegroundColor Cyan
    Write-Host "📁 Archive Service: http://localhost:3002" -ForegroundColor Cyan
    Write-Host "📝 Assessment Service: http://localhost:3003" -ForegroundColor Cyan
    Write-Host "🔔 Notification Service: http://localhost:3005" -ForegroundColor Cyan
    Write-Host "📚 Documentation: http://localhost:8080" -ForegroundColor Cyan
    Write-Host "🐰 RabbitMQ: http://localhost:15672 (admin/admin123)" -ForegroundColor Cyan
    Write-Host ""
}

# Function to stop all services
function Stop-Services {
    Write-Header "Stopping ATMA Services"
    Write-Status "Stopping all containers..."
    docker-compose down --volumes --remove-orphans
    Write-Success "All services stopped!"
}

# Function to clean Docker system
function Clean-Docker {
    Write-Status "Cleaning Docker system..."
    docker-compose down --volumes --remove-orphans 2>$null
    docker system prune -a --volumes -f
    Write-Success "Docker system cleaned!"
}

# Function to start services in simple mode
function Start-Simple {
    param(
        [int]$WorkerCount,
        [bool]$SkipPull,
        [bool]$SkipCloudflare,
        [bool]$SkipDocs
    )
    
    Write-Header "ATMA Simple Start Mode"
    
    if (-not $SkipPull) {
        Write-Status "📥 Pulling latest images..."
        docker-compose pull
    }
    
    Write-Status "🏗️ Starting all services..."
    
    # Build service list
    $services = @("postgres", "redis", "rabbitmq", "auth-service", "assessment-service", 
                  "archive-service", "notification-service", "api-gateway", "testing-service")
    
    # Add analysis workers
    for ($i = 1; $i -le $WorkerCount; $i++) {
        $services += "analysis-worker-$i"
    }
    
    # Add optional services
    if (-not $SkipDocs) {
        $services += "documentation-service"
    }
    
    if (-not $SkipCloudflare) {
        $services += "cloudflared"
    }
    
    # Start all services at once
    docker-compose up --build -d $services
    
    Write-Status "⏳ Waiting for services to start..."
    Start-Sleep -Seconds $Wait
    
    Show-Status
    Write-Success "🎉 ATMA Backend started in simple mode!"
}

# Function to start services in staged mode
function Start-Staged {
    param(
        [int]$WorkerCount,
        [bool]$SkipPull,
        [bool]$SkipCloudflare,
        [bool]$SkipDocs
    )
    
    Write-Header "ATMA Staged Start Mode"
    
    if (-not $SkipPull) {
        Write-Status "📥 Pulling latest images..."
        docker-compose pull
    }
    
    Write-Status "🏗️ Starting services in stages..."
    
    # Stage 1: Infrastructure services
    Write-Step "=== Stage 1: Infrastructure Services ==="
    Start-AndWait -Services @("postgres", "redis", "rabbitmq")
    
    # Stage 2: Core application services
    Write-Step "=== Stage 2: Core Application Services ==="
    Start-AndWait -Services @("auth-service")
    Start-AndWait -Services @("assessment-service", "archive-service", "notification-service")
    
    # Stage 3: API Gateway
    Write-Step "=== Stage 3: API Gateway ==="
    Start-AndWait -Services @("api-gateway")
    
    # Stage 4: Analysis Workers
    Write-Step "=== Stage 4: Analysis Workers ==="
    $workers = @()
    for ($i = 1; $i -le $WorkerCount; $i++) {
        $workers += "analysis-worker-$i"
    }
    docker-compose up --build -d $workers
    
    # Wait for workers (but don't fail if some don't start)
    foreach ($worker in $workers) {
        if (!(Wait-ForService -ServiceName $worker -MaxAttempts 10)) {
            Write-Warning "$worker failed to start"
        }
    }
    
    # Stage 5: Testing service
    Write-Step "=== Stage 5: Testing Service ==="
    Start-AndWait -Services @("testing-service")
    
    # Stage 6: Documentation service (optional)
    if (-not $SkipDocs) {
        Write-Step "=== Stage 6: Documentation Service ==="
        if (!(Start-AndWait -Services @("documentation-service"))) {
            Write-Warning "Documentation service failed to start"
        }
    }
    
    # Stage 7: Cloudflare tunnel (optional)
    if (-not $SkipCloudflare) {
        Write-Step "=== Stage 7: Cloudflare Tunnel ==="
        if (Test-Path ".env") {
            $envContent = Get-Content ".env" -ErrorAction SilentlyContinue
            $tokenLine = $envContent | Where-Object { $_ -match "CLOUDFLARE_TUNNEL_TOKEN=" }
            if ($tokenLine) {
                $token = ($tokenLine -split "=", 2)[1]
                if ($token -and $token -ne "your_token_here") {
                    Write-Status "Starting Cloudflare tunnel..."
                    try {
                        docker-compose up -d cloudflared
                    }
                    catch {
                        Write-Warning "Cloudflare tunnel failed to start (optional): $_"
                    }
                }
                else {
                    Write-Warning "CLOUDFLARE_TUNNEL_TOKEN not properly set, skipping"
                }
            }
            else {
                Write-Warning "CLOUDFLARE_TUNNEL_TOKEN not found in .env, skipping Cloudflare tunnel"
            }
        }
        else {
            Write-Warning "No .env file found, skipping Cloudflare tunnel"
        }
    }
    
    Show-Status
    Write-Success "🎉 ATMA Backend started in staged mode!"
}

# Main execution
function Main {
    # Handle help and version
    if ($Help) {
        Show-Help
        return
    }
    
    if ($Version) {
        Write-Host "$SCRIPT_NAME v$SCRIPT_VERSION"
        return
    }
    
    # Check Docker availability
    Test-Docker
    
    # Handle special commands
    if ($Status) {
        Show-Status
        return
    }
    
    if ($Stop) {
        Stop-Services
        return
    }
    
    if ($Restart) {
        Stop-Services
        Start-Sleep -Seconds 2
        # Continue to start services
    }
    
    # Set defaults based on mode
    if ($Quick) {
        $NoPull = $true
        $Clean = $false
    }
    
    # Default to staged mode if no mode specified
    if (-not $Simple -and -not $Staged) {
        $Staged = $true
    }
    
    # Clean Docker if requested
    if ($Clean) {
        Clean-Docker
    }
    
    # Start services based on mode
    try {
        if ($Simple) {
            Start-Simple -WorkerCount $Workers -SkipPull $NoPull -SkipCloudflare $NoCloudflare -SkipDocs $NoDocs
        }
        else {
            Start-Staged -WorkerCount $Workers -SkipPull $NoPull -SkipCloudflare $NoCloudflare -SkipDocs $NoDocs
        }
        
        # Show final information
        Write-Host ""
        Write-Status "🧪 To run tests:"
        Write-Host "   .\run-tests.ps1"
        Write-Host "   node testing/e2e-test.js"
        Write-Host "   node testing/load-test.js"
        Write-Host ""
        Write-Status "🔧 Useful commands:"
        Write-Host "   .\start-atma.ps1 -Status     # Check service status"
        Write-Host "   .\start-atma.ps1 -Stop       # Stop all services"
        Write-Host "   .\start-atma.ps1 -Restart    # Restart all services"
        Write-Host ""
    }
    catch {
        Write-Error "Startup failed: $_"
        Write-Status "Checking service status..."
        docker-compose ps
        exit 1
    }
}

# Run main function
Main
